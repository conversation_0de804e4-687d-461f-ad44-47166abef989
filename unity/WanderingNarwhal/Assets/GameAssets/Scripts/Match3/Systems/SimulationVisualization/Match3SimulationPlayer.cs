using System;
using System.Collections;
using System.Collections.Generic;
using BBB.Core;
using BBB.DI;
using BBB.Match3.Renderer;
using BBB.Match3.Systems.CreateSimulationSystems;
using BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes;
using BBB.RaceEvents;
using BBB.UI;
using BebopBee.Core.Extensions;
using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Core;
using GameAssets.Scripts.Match3.Logic;
using UnityEngine;

namespace BBB.Match3.Systems
{
    public class Match3SimulationPlayer : IContextInitializable
    {
        public class SimulationTracker
        {
            private readonly List<(int turn, List<Match3ActionBase> actions)> _allSimulationSteps;
            private readonly List<Match3ActionBase> _preparedActions;
            private readonly List<int> _turnsForActions;
            
            public SimulationTracker(GameSimulation simulation)
            {
                _allSimulationSteps = new List<(int turn, List<Match3ActionBase> actions)>(simulation.GetAllSimulationSteps());

                var tuple = PrepareActionsCollection();
                _preparedActions = tuple.Item1;
                _turnsForActions = tuple.Item2;
            }

            private (List<Match3ActionBase>, List<int>) PrepareActionsCollection()
            {
                var actionList = new List<Match3ActionBase>();
                var turnsList = new List<int>();

                foreach (var stepTuple in _allSimulationSteps)
                {
                    foreach (var action in stepTuple.actions)
                    {
                        actionList.Add(action);
                        turnsList.Add(stepTuple.turn);
                    }
                }

                return (actionList, turnsList);
            }

            public bool ShouldEndSimulation => _preparedActions.Count == 0;

            public (Match3ActionBase, int) PeekNextAction()
            {
                if (_preparedActions.Count > 0)
                {
                    return (_preparedActions[0], _turnsForActions[0]);
                }

                return (null, 0);
            }

            public Match3ActionBase PopNextAction()
            {
                if (_preparedActions.Count > 0)
                {
                    var action = _preparedActions[0];
                    _preparedActions.RemoveAt(0);
                    _turnsForActions.RemoveAt(0);
                    return action;
                }

                return null;
            }

            public IEnumerable<Match3ActionBase> GetAllActions()
            {
                return _preparedActions;
            }
        }

        private const int TurnForVictoryAction = 1;
        
        private bool _interrupted;
        private SimulationTracker _currentlyPlayingTracker;
        private Grid _currentlyPlayingGrid;
        private Coroutine _playRoutine;

#if UNITY_EDITOR
        public GravitySystem GravitySystemDEBUG { get; private set; }
#endif

        private M3SpawnSystem _spawnSystem;
        private GameEventMatch3ManagersCollection _gameEventMatch3ManagersCollection;
        private IRaceEventMatch3Manager _raceEventMatch3Manager;
        private IRoyaleEventMatch3Manager _royaleEventMatch3Manager;       
        private ITeamCoopEventMatch3Manager _teamEventMatch3Manager;
        private IEventDispatcher _eventDispatcher;
        private PlaySimulationActionProxy _proxy;
        private IAssistParamsProvider _assistParamsProvider;
        private SpawnerSettingsManager _spawnerSettingsManager;
        private ILevelAnalyticsReporter _levelAnalyticsReporter;

        private readonly List<Coords> _releasedCoords = new();
        private readonly Pool<HashSet<Match3ActionBase>> _allAsyncActionsPool = new Pool<HashSet<Match3ActionBase>>(() => new HashSet<Match3ActionBase>());
        private readonly Pool<List<Match3ActionBase>> _actionPriorityListPool = new Pool<List<Match3ActionBase>>(1, () => new List<Match3ActionBase>());
        private readonly Pool<Dictionary<Coords, List<Match3ActionBase>>> _actionPriorityPool = new Pool<Dictionary<Coords, List<Match3ActionBase>>>(1, () => new Dictionary<Coords, List<Match3ActionBase>>());

        private Queue<SimulationTracker> _simulationTrackers = new ();
        private int _currentOffset;
        private bool _logicalSimulationFinished;

        public int ShuffleCount { get; private set; }

        public void InitializeByContext(IContext context)
        {
            _spawnSystem = context.Resolve<M3SpawnSystem>().Clone();
            _gameEventMatch3ManagersCollection = context.Resolve<GameEventMatch3ManagersCollection>();
            _raceEventMatch3Manager = context.Resolve<IRaceEventMatch3Manager>();
            _royaleEventMatch3Manager = context.Resolve<IRoyaleEventMatch3Manager>();
            _teamEventMatch3Manager = context.Resolve<ITeamCoopEventMatch3Manager>();
            _proxy = new PlaySimulationActionProxy(context);
            _assistParamsProvider = context.Resolve<IAssistParamsProvider>();
            _spawnerSettingsManager = context.Resolve<SpawnerSettingsManager>();
            _eventDispatcher = context.Resolve<IEventDispatcher>();
            _levelAnalyticsReporter = context.Resolve<ILevelAnalyticsReporter>();
        }

        public void ResetDefaults()
        {
            _interrupted = default;
            _currentlyPlayingTracker = default;
            _currentlyPlayingGrid = default;
            _playRoutine = default;
#if UNITY_EDITOR
            GravitySystemDEBUG = default;
#endif
        }

        public void Hide()
        {
            Complete();
            if (_proxy != null && _proxy.GameController != null && _playRoutine != null)
            {
                _proxy.GameController.StopCoroutine(_playRoutine);
            }

            _playRoutine = null;
            
            lock (_simulationTrackers)
            {
                _simulationTrackers.Clear();
            }
            _currentlyPlayingTracker = null;
            _currentlyPlayingGrid = null;
        }

        private void SetupEventsManagers(ILevel level, Action onEventsManagersSetup = null)
        {
            _gameEventMatch3ManagersCollection.ForEveryManager(manager => manager.Setup(level));
            _raceEventMatch3Manager.Setup(level);
            _royaleEventMatch3Manager.Setup(level);
            _teamEventMatch3Manager.Setup(level);
            onEventsManagersSetup?.Invoke();
        }

        public void RunInitializeBoardProcess(Grid grid, ILevel level, int remainingMoves, Action onEventsManagersSetup = null)
        {
            ShuffleCount = 0;
            var gridClone = grid.Clone();
            _spawnSystem.Setup(level);
            SetupEventsManagers(level, onEventsManagersSetup);

            BDebug.Log(LogCat.Match3, $"Starting level '{level.Config.Uid}', [seed = {RandomSystem.Seed}].\ntotal existing spawners({_spawnerSettingsManager?.SpawnerSettings?.Length})");
            var simParams = new SimulationInputParams
            {
                UsedKinds = _proxy.GameController.Level.UsedKinds,
                Settings = _proxy.GameController.Settings,
                SimulateLoopException = _proxy.GameController.SimulateLoopException,
                TurnsLimit = _proxy.GameController.Level.TurnsLimit,
                OriginalGrid = _proxy.GameController.OriginalGrid.Clone()
            };

            var gravitySystem = new GravitySystem(simParams, _proxy.GoalsSystem, _proxy.TileResourceSelector, _assistParamsProvider, _levelAnalyticsReporter);

            var sim = gravitySystem.CreateSimulationSync(grid: gridClone, playerInput: new PlayerInputNone(), remainingMoves: remainingMoves, assistParams: null, spawnSystem: _spawnSystem);
            if (sim.Result == SimulationResult.SuccessShuffleLose)
            {
                const int restartsCount = 10;
                BDebug.LogError(LogCat.Match3, $"Failed to start game due to failed shuffle. Trying to restart {restartsCount} times. (levelUid={grid.DebugCurrentLevelUid})");
                bool canStartGameWithoutShuffle = false;
                for (int i = 0; i < restartsCount; i++)
                {
                    gridClone = grid.Clone();
                    gravitySystem = new GravitySystem(simParams, _proxy.GoalsSystem, _proxy.TileResourceSelector, _assistParamsProvider, _levelAnalyticsReporter);
                    sim = gravitySystem.CreateSimulationSync(grid: gridClone, playerInput: new PlayerInputNone(), remainingMoves: remainingMoves, assistParams: null, spawnSystem: _spawnSystem);
                    if (sim.Result != SimulationResult.SuccessShuffleLose)
                    {
                        BDebug.LogError(LogCat.Match3, $"Restart success at attempt {(i + 1)}. (levelUid={grid.DebugCurrentLevelUid})");
                        canStartGameWithoutShuffle = true;
                        break;
                    }
                    else
                    {
                        BDebug.LogError(LogCat.Match3, $"Failed restart attempt {(i + 1)}. (levelUid={grid.DebugCurrentLevelUid})");
                    }
                }

                if (!canStartGameWithoutShuffle)
                {
                    BDebug.LogError(LogCat.Match3, $"All {restartsCount} restart attempts failed due to failed shuffle. Level is not playable.");
                }
            }

            ApplyInitializeBoardProcessActions(sim);

            _proxy.GridController.Clear();
            _proxy.GameController.Level.Grid = gridClone;
            _proxy.GridController.SetupGrid(gridClone);
        }

        private void ApplyInitializeBoardProcessActions(GameSimulation sim)
        {
            var tracker = new SimulationTracker(sim);
            foreach (var action in tracker.GetAllActions())
            {
                if (action is InitializeBoardProcessAction initializeBoardAction)
                {
                    initializeBoardAction.ExecuteForBoardInitialize(_proxy);
                }
            }
        }

        public void Complete()
        {
            _interrupted = true;

            if (_currentlyPlayingTracker != null && _currentlyPlayingGrid != null)
            {
                while (!_currentlyPlayingTracker.ShouldEndSimulation)
                {
                    var (action, _) = _currentlyPlayingTracker.PeekNextAction();

                    if (action == null)
                        break;

                    if (!action.HasFinished)
                    {
                        action.CompletionExecution(_currentlyPlayingGrid, _proxy);
                    }

                    _currentlyPlayingTracker.PopNextAction();
                }

                void CompleteAction(Tile tile, Coords coords)
                {
                    tile.Remove(TileState.InTransition);
                    var tileView = _proxy.TileController.GetOrCreateTileView(tile);
                    if (tileView != null)
                    {
                        tileView.TileMotionController.SetPosAndFreeze(coords.ToUnityVector2());
                        tileView.Animator.Settle();
                    }
                }

                _currentlyPlayingGrid.ForeachTile(CompleteAction);
                _proxy.TileController.Update(_currentlyPlayingGrid, true);
                _proxy.GridController.DimBoard(false);

                _currentlyPlayingTracker = null;
                _currentlyPlayingGrid = null;
            }
        }

        public bool IsPlayingSimulationFor(Grid grid)
        {
            return _currentlyPlayingGrid == grid;
        }

        private void ForceCompleteCurrentSimulation()
        {
            Complete();
            if (_playRoutine != null)
                _proxy.GameController.StopCoroutine(_playRoutine);
        }

        public SimulationResult SimulateSync(Grid originalGrid, Grid grid, ILevel level, IPlayerInput playerInput, 
            int remainingMoves, ExtraBoostersHelper.PendingBoosters pendingBoosters = null, bool visualSimulation = true)
        {
            if (IsPlayingSimulationFor(grid))
            {
                BDebug.LogError(LogCat.Match3,"Simulation is already playing for this grid and will be force completed!");
                ForceCompleteCurrentSimulation();
            }

            PrepareGrid(grid);
            _interrupted = false;
            var gridClone = grid.Clone();
            var goalSystem = _proxy.GoalsSystem;
            var totalMoves = _proxy.GameController.Level.TurnsLimit;
            var originalGoals = _proxy.GoalsSystem.OriginalGoals;
            var originalAssistState = new AssistState(originalGoals, originalGrid);
            var assistProgressAchieved = originalAssistState - new AssistState(_proxy.GoalsSystem.GoalProgressLeft, _proxy.GameController.Grid);
            var assistParams = _assistParamsProvider.GetAssistParams(remainingMoves, totalMoves, assistProgressAchieved, originalAssistState);
            _spawnSystem.Setup(level);
            var simParams = new SimulationInputParams
            {
                UsedKinds = _proxy.GameController.Level.UsedKinds,
                Settings = _proxy.GameController.Settings,
                SimulateLoopException = _proxy.GameController.SimulateLoopException,
                TurnsLimit = _proxy.GameController.Level.TurnsLimit,
                OriginalGrid = _proxy.GameController.OriginalGrid.Clone(),
                PendingBoosters = pendingBoosters
            };

            var gravitySystem = new GravitySystem(simParams, goalSystem, tileResources: _proxy.TileResourceSelector, _assistParamsProvider, _levelAnalyticsReporter);
            var simulation = gravitySystem.CreateSimulationSync(gridClone, playerInput, remainingMoves, assistParams, _spawnSystem);
            _logicalSimulationFinished = true;

            if (!simulation.IsNotEmpty)
                return SimulationResult.Fail;

            ShuffleCount += simulation.ShuffleCount;
            
            if (visualSimulation)
            {
                _currentlyPlayingTracker = new SimulationTracker(simulation);
                _currentlyPlayingGrid = grid;
                if (!_interrupted)
                {
                    lock (_simulationTrackers)
                    {
                        _simulationTrackers.Enqueue(_currentlyPlayingTracker);
                    }
                }

                if (_playRoutine == null)
                {
                    _playRoutine = _proxy.GameController.StartCoroutineMethod(PlaySimulation(grid));
                }
            }

            return simulation.Result;
        }

        public bool IsPlayingVisualSimulation()
        {
            return _playRoutine != null;
        }

        public async UniTask<SimulationResult> SimulateAsync(Grid originalGrid, Grid grid, ILevel level,
            IPlayerInput playerInput, int remainingMoves, ExtraBoostersHelper.PendingBoosters pendingBoosters, Action<SimulationResult> onSimulationResult)
        {
            if (IsPlayingSimulationFor(grid))
            {
                await UniTask.WaitWhile(this, state => !state._logicalSimulationFinished);
            }

            BDebug.Log(LogCat.Match3, $"SimulateAsync started, InputLock = true");
            PrepareGrid(grid);
            _interrupted = false;
            var gridClone = grid.Clone();
            var goalSystem = _proxy.GoalsSystem;
            var totalMoves = _proxy.GameController.Level.TurnsLimit;
            var originalGoals = _proxy.GoalsSystem.OriginalGoals;
            var originalAssistState = new AssistState(originalGoals, originalGrid);
            var assistProgressAchieved = originalAssistState - new AssistState(_proxy.GoalsSystem.GoalProgressLeft, _proxy.GameController.Grid);
            var assistParams = _assistParamsProvider.GetAssistParams(remainingMoves, totalMoves, assistProgressAchieved, originalAssistState);

            _spawnSystem.Setup(level);

            var simParams = new SimulationInputParams
            {
                UsedKinds = _proxy.GameController.Level.UsedKinds,
                Settings = _proxy.GameController.Settings,
                SimulateLoopException = _proxy.GameController.SimulateLoopException,
                TurnsLimit = _proxy.GameController.Level.TurnsLimit,
                OriginalGrid = _proxy.GameController.OriginalGrid.Clone(),
                PendingBoosters = pendingBoosters,
            };

            var gravitySystem = new GravitySystem(simParams, goalSystem, _proxy.TileResourceSelector, _assistParamsProvider, _levelAnalyticsReporter);
#if UNITY_EDITOR
            GravitySystemDEBUG = gravitySystem;
#endif
            var task = new UniTaskCompletionSource<SimulationResult>();
            goalSystem.StartNewTurn();
            gravitySystem.ClearQueue();
            _logicalSimulationFinished = false;
            _currentlyPlayingGrid = grid;
            _playRoutine = _proxy.GameController.StartCoroutineMethod(PlaySimulation(grid));
            var simResult = SimulationResult.Fail;
            UniTask.RunOnThreadPool(async () =>
            {
                bool checkForMatches;
                var levelEnded = false;
                do
                {
                    var simulation = gravitySystem.CreateSimulationAsync(
                        gridClone,
                        playerInput,
                        remainingMoves,
                        assistParams,
                        _spawnSystem);
                    simulation.RemoveLastEmptyTurnsFromLogicalActions();
                    ShuffleCount += simulation.ShuffleCount;
                    if (!levelEnded)
                    {
                        simResult = simulation.Result;
                    }

                    if (!simulation.IsNotEmpty) break;

                    checkForMatches = !simulation.CheckTurnForAction(TurnForVictoryAction,
                        action => action is ActionStarWave or ActionAssistLog) && simulation.Result != SimulationResult.Fail;
#if UNITY_EDITOR
                    if (M3Editor.IntegrationTests.IntegrationTestTracker.IsIterationTestMode())
                    {
                        checkForMatches &= simResult != SimulationResult.SuccessShuffleLose;
                    }
#endif
                    
                    await UniTask.SwitchToMainThread();
                    task.TrySetResult(simResult);
                    if (!levelEnded && simResult == SimulationResult.SuccessWin)
                    {
                        levelEnded = true;
                        onSimulationResult.SafeInvoke(SimulationResult.SuccessWin);
                    }
                    await UniTask.SwitchToThreadPool();

                    if (!_interrupted)
                    {
                        lock (_simulationTrackers)
                        {
                            var tracker = new SimulationTracker(simulation);
                            _simulationTrackers.Enqueue(tracker);
                        }
                    }

                    playerInput = new PlayerInputEmpty();

                    if (!checkForMatches)
                    {
                        var areAllBoostersPlaced = gravitySystem.ProcessPendingBoosters(simParams, gridClone);

                        if (areAllBoostersPlaced && !_interrupted)
                        {
                            lock (_simulationTrackers)
                            {
                                var tracker = new SimulationTracker(simulation);
                                _simulationTrackers.Enqueue(tracker);
                            }

                            checkForMatches = true;
                        }
                    }
                } while (checkForMatches);

                _logicalSimulationFinished = true;
                gravitySystem.Uninit();
                
                if (level == null) return;
                
                await UniTask.SwitchToMainThread();
                if (simResult != SimulationResult.SuccessWin)
                {
                    onSimulationResult.SafeInvoke(simResult);
                }
                if (simResult == SimulationResult.Fail)
                {
                    task.TrySetResult(simResult);
                }

                await UniTask.WaitWhile(this, state => state._playRoutine != null);

                if (playerInput.ShouldEndSimulation())
                {
                    _proxy.GameController.SimulationEnded();
                }
            }).Forget(exception => BDebug.LogError(LogCat.Match3, exception));
            
            await task.Task;

            return simResult;
        }

        private void PrepareGrid(Grid grid)
        {
            static void RemoveAction(Tile t, Coords c)
            {
                t.Remove(TileState.InTransition);
            }

            grid.ForeachTile(RemoveAction);
        }


        private IEnumerator PlaySimulation(Grid grid)
        {
            _currentOffset = 0;
            
            HashSet<Match3ActionBase> allAsyncActions = _allAsyncActionsPool.Obtain();
            List<Match3ActionBase> actionPriorityList = _actionPriorityListPool.Obtain();
            Dictionary<Coords, List<Match3ActionBase>> actionPriority = _actionPriorityPool.Obtain();
            _proxy.TileTickPlayer.BoardObjectFactory.ClearFreeFallTargetPriority();

#if BBB_LOG_M3_SIMULATION
            var actualExecution = new List<string>(actionPriorityList.Count);
#endif

            while (actionPriorityList.Count > 0 || !_logicalSimulationFinished || _simulationTrackers.Count > 0)
            {
                lock (_simulationTrackers)
                {
                    while (_simulationTrackers.Count > 0)
                    {
                        _currentlyPlayingTracker = _simulationTrackers.Dequeue();
                        var allActions = _currentlyPlayingTracker.GetAllActions();
#if BBB_LOG_M3_SIMULATION
                        BDebug.LogDebugError($"Adding action to play: {string.Join("\n", allActions)} at time {Time.time}");
#endif
                        BuildActionPriorityList(allActions, actionPriorityList, actionPriority);
                        _currentOffset += actionPriorityList.Count;
                        _proxy.TileTickPlayer.BoardObjectFactory.BuildFreeFallTargetPriority(allActions);
                    }
                }

                if (_interrupted)
                {
                    ResetCollections();
                    yield break;
                }

                bool isFirst = true;
                foreach (var nextAction in actionPriorityList)
                {
                    if (nextAction == null)
                        break;

                    if (_interrupted)
                        break;

                    if (!isFirst && nextAction.ShouldExecuteAfterAllPreviousFinished())
                    {
                        if (nextAction.ShouldBlockOtherActions())
                        {
                            break;
                        }

                        continue;
                    }

                    if (isFirst && nextAction.ShouldExecuteAfterAllPreviousFinished() && allAsyncActions.Count > 0 && nextAction.ShouldWaitForAsyncActions())
                    {
                        break;
                    } 

                    isFirst = false;
                    var beingExecuted = nextAction.ExecutionStarted;
#if BBB_LOG_M3_SIMULATION
                    var loggedWait = nextAction.LoggedWait;
#endif
                    if (!nextAction.ExecutionStarted && CanBeExecuted(nextAction, actionPriority) || nextAction.ExecutionStarted)
                    {
                        _releasedCoords.Clear();
                        nextAction.Execute(grid, _proxy, _releasedCoords);
                        ReportActionCompleted(_releasedCoords, nextAction, actionPriority);
#if BBB_LOG_M3_SIMULATION
                        if (!beingExecuted && nextAction.ExecutionStarted)
                            actualExecution.Add($"{nextAction}" +
                                                $" at time {Time.time}");
                        if (!loggedWait && nextAction.LoggedWait)
                        {
                            LogOrderOfExecution();
                        }
#endif
                    }

                    if (!nextAction.ExecutionStarted) continue;

                    // If this action is waiting for completion, then we should not continue processing tasks after this
                    // one. However, we should still allow previous actions to be completed (if there's any)
                    if (nextAction.WaitingForCompletion) break;

                    if (nextAction.HasFinished && !nextAction.HasFinishedAsync)
                    {
                        allAsyncActions.Add(nextAction);
                    }
                }

                yield return null;

                actionPriorityList.RemoveAll(action => action is null || action.HasFinished && !action.WaitingForCompletion);
                allAsyncActions.RemoveWhere(action => action is null || action.HasFinishedAsync);
            }

#if BBB_LOG_M3_SIMULATION
            void LogOrderOfExecution()
            {
                BDebug.LogDebugError($"Order of execution:\n{string.Join("\n", actualExecution)}");
            }

            LogOrderOfExecution();
#endif

            while (allAsyncActions.Count > 0)
            {
                yield return null;
                allAsyncActions.RemoveWhere(action => action is null || action.HasFinishedAsync);
            }

            if (_interrupted)
            {
                ResetCollections();
                yield break;
            }

            if (_currentlyPlayingGrid != null)
            {
                void RemoveAction(Tile t, Coords c)
                {
                    t.Remove(TileState.InTransition);
                }

                _currentlyPlayingGrid.ForeachTile(RemoveAction);
            }

            _currentlyPlayingGrid = null;

            UpdateAssistParams();
            ResetCollections();
            SimulationPlayUtils.ClearDebugInfo();
            _playRoutine = null;
            yield break;

            void ResetCollections()
            {
                allAsyncActions.Clear();
                _allAsyncActionsPool.Free(allAsyncActions);
                actionPriorityList.Clear();
                _actionPriorityListPool.Free(actionPriorityList);
                actionPriority.Clear();
                _actionPriorityPool.Free(actionPriority);
            }
        }

        private void UpdateAssistParams()
        {
            var remainingMoves = _proxy.GameController.RemainingMoves;
            var totalMoves = _proxy.GameController.Level.TurnsLimit;
            var originalGrid = _proxy.GameController.OriginalGrid;
            var originalGoals = _proxy.GoalsSystem.OriginalGoals;
            var originalAssistState = new AssistState(originalGoals, originalGrid);
            var assistProgressAchieved = originalAssistState - new AssistState(_proxy.GoalsSystem.GoalProgressLeft, _proxy.GameController.Grid);
            var assistParams = _assistParamsProvider.GetAssistParams(remainingMoves, totalMoves, assistProgressAchieved, originalAssistState);

            var assistParamsUpdated = _eventDispatcher.GetMessage<AssistParamsUpdated>();
            assistParamsUpdated.AssistParams = assistParams;
            assistParamsUpdated.AssistProgressAchieved = assistProgressAchieved;
            assistParamsUpdated.OriginalAssistState = originalAssistState;
            _eventDispatcher.TriggerEventNextFrame(assistParamsUpdated);

        }

        private void ReportActionCompleted(List<Coords> releasedCoords, Match3ActionBase nextAction, Dictionary<Coords, List<Match3ActionBase>> actionPriority)
        {
            if (releasedCoords == null) return;

            foreach (var coord in releasedCoords)
            {
                if (!actionPriority.ContainsKey(coord) || actionPriority[coord].Count == 0 || !actionPriority[coord][0].Equals(nextAction))
                {
#if BBB_LOG
                    BDebug.LogError(LogCat.Match3, $"Tried to report action completed on coord {coord} but action {nextAction} was not in first place.");
#endif
                    continue;
                }

                actionPriority[coord].RemoveAt(0);
            }
        }

        private bool CanBeExecuted(Match3ActionBase nextAction, Dictionary<Coords, List<Match3ActionBase>> actionPriority)
        {
            var allCoords = nextAction.GetAffectedCoords();
            foreach (var coord in allCoords)
            {
                if (!actionPriority.ContainsKey(coord) 
                    || actionPriority[coord].Count == 0 
                    || !actionPriority[coord][0].Equals(nextAction))
                {
                    return false;
                }
            }

            return true;
        }

        private void BuildActionPriorityList(IEnumerable<Match3ActionBase> allActions, List<Match3ActionBase> actionPriorityList, Dictionary<Coords, List<Match3ActionBase>> actionPriority)
        {
            var priority = _currentOffset;
            foreach (var action in allActions)
            {
                action.Priority = priority++;
                actionPriorityList.Add(action);
                var allCoords = action.GetAffectedCoords();
                if (allCoords == null) continue;

                foreach (var coord in allCoords)
                {
                    if (!actionPriority.ContainsKey(coord))
                    {
                        actionPriority.Add(coord, new List<Match3ActionBase>());
                    }

                    actionPriority[coord].Add(action);
                }
            }
        }
    }

    public static class SimulationResultExtensions
    {
        public static bool IsWin(this SimulationResult simResult)
        {
            return simResult == SimulationResult.SuccessWin;
        }

        public static bool IsLose(this SimulationResult simResult)
        {
            return simResult == SimulationResult.Fail || simResult == SimulationResult.SuccessShuffleLose;
        }
    }

    /// <summary>
    /// Simulation result of one turn of the level.
    /// </summary>
    public enum SimulationResult
    {
        /// <summary>
        /// Error occurred during simulation.
        /// </summary>
        Fail,

        /// <summary>
        /// Simulation ended without any errors.
        /// </summary>
        Success,

        /// <summary>
        /// Simulation ended and level has been won.
        /// </summary>
        SuccessWin,

        /// <summary>
        /// Simulation ended successfully and level lose happened because grid doesn't have any valid move and shuffle didn't help.
        /// </summary>
        /// <remarks>
        /// This rarely can happen when level contains growing sand mechanic or / and blockers spawning.
        /// </remarks>
        SuccessShuffleLose,
    }
}