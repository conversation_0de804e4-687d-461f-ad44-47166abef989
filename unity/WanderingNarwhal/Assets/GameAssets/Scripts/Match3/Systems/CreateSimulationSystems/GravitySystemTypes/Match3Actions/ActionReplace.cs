using Cysharp.Threading.Tasks;
using GameAssets.Scripts.Match3.Logic;

namespace BBB.Match3.Systems.CreateSimulationSystems.GravitySystemTypes
{
    public sealed class ActionReplace : Match3ActionBase
    {
        private readonly Coords _start;
        private readonly Tile _tile;
        private readonly GoalType _goalType;
        private readonly bool _isVisible;
        
        public ActionReplace(Coords start, Tile tile, GoalType goalType = GoalType.None, bool isVisible = true)
        {
            _start = start;
            _tile = tile.Clone();
            _goalType = goalType;
            _isVisible = isVisible;
            
            AffectedCoords.Add(_start);
        }

        private bool ModifyGrid(Grid grid, out Cell cell, out int tileId)
        {
            cell = grid.FindCellByTileId(_tile.Id);
            tileId = -1;

            if(cell == null)
                cell = grid.GetCell(_start);

            if (cell == null)
            {
                UnityEngine.Debug.LogErrorFormat("Cell {0} not found during replace", _start);
                return false;
            }

            if (cell.HasTile())
            {
                tileId = _tile.Id;
            }
            
            cell.ReplaceTile(_tile);

            return true;
        }

        public override void CompletionExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            ModifyGrid(grid, out var cell, out var tileId);
        }

        protected override void InitialExecution(Grid grid, PlaySimulationActionProxy proxy)
        {
            if (ModifyGrid(grid, out Cell cell, out int tileId))
            {
                if (tileId != -1)
                {
                    proxy.TileTickPlayer.BoardObjectFactory.RemoveFutureTargetLock(_start, tileId);
                }
                
                if (proxy.TileController == null)
                {
                    UnityEngine.Debug.LogError("TileController is null during replace");
                    return;
                }

                var tileView =  proxy.TileController.GetOrCreateTileView(_tile);

                if (tileView != null)
                    tileView.UpdateView(cell, cell.Tile, cell.Coords, true, _isVisible);

                if (_goalType != GoalType.None)
                {
                    proxy.GoalPanel.AddGoals(grid, proxy.GoalsSystem, _goalType).Forget();
                }
            
                ReleasedCoords.Add(_start);
            }
        }
        protected override string GetMembersString()
        {
            return $"tileId={_tile.Id} coords={_start}";
        }
    }

}